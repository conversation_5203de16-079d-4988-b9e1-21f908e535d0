#!/bin/bash

# Script to update VM configuration to boot from the Windows 10 ISO

# Define VM path
VM_PATH="/Users/<USER>/Documents/GitHub/TikTokBotFarmMVP/scripts/vmware-setup/vm-data/Windows10TikTokBot.vmx"
ISO_PATH="/Users/<USER>/Documents/GitHub/TikTokBotFarmMVP/scripts/vmware-setup/vm-data/windows10.iso"

# Check if VM exists
if [ ! -f "$VM_PATH" ]; then
    echo "VM configuration file not found: $VM_PATH"
    exit 1
fi

# Check if ISO exists
if [ ! -f "$ISO_PATH" ]; then
    echo "Windows 10 ISO not found: $ISO_PATH"
    exit 1
fi

# Create a backup of the VMX file
echo "Creating backup of VM configuration..."
cp "$VM_PATH" "${VM_PATH}.bak"

# Check if the VM is running
if vmrun -T fusion list | grep -q "$VM_PATH"; then
    echo "VM is running. Stopping VM..."
    vmrun -T fusion stop "$VM_PATH" hard
    sleep 5
fi

# Update the VM configuration
echo "Updating VM configuration..."

# Ensure the CD/DVD drive is properly configured
# First, let's directly set the ISO path
echo "Setting CD/DVD drive to use our Windows 10 ISO..."
sed -i '' 's|ide0:0.fileName = ".*"|ide0:0.fileName = "'"$ISO_PATH"'"|g' "$VM_PATH"
sed -i '' 's|ide0:0.startConnected = ".*"|ide0:0.startConnected = "TRUE"|g' "$VM_PATH"
sed -i '' 's|ide0:0.deviceType = ".*"|ide0:0.deviceType = "cdrom-image"|g' "$VM_PATH"

# If the CD/DVD configuration doesn't exist, add it
if ! grep -q "ide0:0.present" "$VM_PATH"; then
    echo 'ide0:0.present = "TRUE"' >> "$VM_PATH"
    echo 'ide0:0.fileName = "'"$ISO_PATH"'"' >> "$VM_PATH"
    echo 'ide0:0.deviceType = "cdrom-image"' >> "$VM_PATH"
    echo 'ide0:0.startConnected = "TRUE"' >> "$VM_PATH"
fi

# Add boot settings
if ! grep -q "bios.bootDelay" "$VM_PATH"; then
    echo 'bios.bootDelay = "5000"' >> "$VM_PATH"
fi

if ! grep -q "bios.bootOrder" "$VM_PATH"; then
    echo 'bios.bootOrder = "cdrom,hdd"' >> "$VM_PATH"
fi

# Remove VMware Tools installation state entries if they exist
sed -i '' '/toolsInstallManager/d' "$VM_PATH"

echo "VM configuration updated successfully."

# Also use vmrun setCDMedia as an additional way to set the ISO
echo "Using vmrun setCDMedia to set the ISO..."
vmrun -T fusion setCDMedia "$VM_PATH" "$ISO_PATH" || echo "Note: setCDMedia command failed, but this is not critical as we've already updated the VMX file."
echo "Starting VM..."
vmrun -T fusion start "$VM_PATH" gui

echo "VM should now boot from the Windows 10 ISO."
echo "Follow the Windows installation prompts in the VM window."
echo ""
echo "After Windows is installed, you'll need to:"
echo "1. Install VMware Tools (Virtual Machine > Install VMware Tools)"
echo "2. Install LDPlayer from https://www.ldplayer.net/"
echo "3. Install TikMatrix from https://tikmatrix.com/"
echo "4. Configure them according to the TikTok Bot Farm documentation"
