#!/bin/bash

# Script to force cleanup a stuck VMware Tools installation

# Define VM path
VM_PATH="/Users/<USER>/Documents/GitHub/TikTokBotFarmMVP/scripts/vmware-setup/vm-data/Windows10TikTokBot_EasyInstall/Windows10TikTokBot_EasyInstall.vmx"

echo "Forcefully cleaning up stuck VMware Tools installation..."

# Force stop the VM
echo "Force stopping the VM..."
vmrun -T fusion stop "$VM_PATH" hard

# Wait for VM to completely stop
echo "Waiting for VM to stop..."
sleep 10

# Clear VMware Tools installation state
echo "Clearing VMware Tools installation state..."
sed -i '' '/toolsInstall/d' "$VM_PATH"

# Start the VM
echo "Starting VM..."
vmrun -T fusion start "$VM_PATH" gui

# Wait for VM to boot
echo "Waiting for VM to boot..."
sleep 30

# Try to install VMware Tools again
echo "Attempting to install VMware Tools..."
vmrun -T fusion installTools "$VM_PATH"

echo "Please complete these steps in the VM:"
echo "1. Open File Explorer"
echo "2. Navigate to the DVD drive (usually D: or E:)"
echo "3. Run setup64.exe"
echo "4. Follow the installation prompts"
echo "5. Restart the VM when prompted"
