#!/bin/bash

# Script to create a new VM using VMware Easy Install

# Define VM name and paths
VM_NAME="Windows10TikTokBot_EasyInstall"
VM_DIR="vm-data/$VM_NAME"
VM_PATH="$VM_DIR/$VM_NAME.vmx"
ISO_PATH="/Users/<USER>/Downloads/Win10_22H2_EnglishInternational_x64v1.iso"

# Check if VMware Fusion is installed
if [ ! -d "/Applications/VMware Fusion.app" ]; then
    echo "VMware Fusion is not installed. Please install it first."
    exit 1
fi

# Check if ISO exists
if [ ! -f "$ISO_PATH" ]; then
    echo "Windows 10 ISO not found: $ISO_PATH"
    echo "Please make sure the Windows 10 ISO is in the vm-data directory."
    exit 1
fi

# Check if VM already exists
if [ -d "$VM_DIR" ]; then
    echo "VM directory already exists: $VM_DIR"
    echo "Removing existing VM..."
    rm -rf "$VM_DIR"
fi

# Create VM directory
mkdir -p "$VM_DIR"

# Use VMware Fusion's vmware-vdiskmanager to create a virtual disk
echo "Creating virtual disk..."
# Make sure the directory exists
mkdir -p "$VM_DIR"
# Create the virtual disk
"/Applications/VMware Fusion.app/Contents/Library/vmware-vdiskmanager" -c -s 80GB -a ide -t 0 "$VM_DIR/$VM_NAME.vmdk"
# Verify the disk was created
if [ ! -f "$VM_DIR/$VM_NAME.vmdk" ]; then
    echo "Failed to create virtual disk: $VM_DIR/$VM_NAME.vmdk"
    echo "Creating disk using alternative method..."
    # Try alternative method
    "/Applications/VMware Fusion.app/Contents/Library/vmware-vdiskmanager" -c -s 80GB -t 0 "$VM_DIR/$VM_NAME.vmdk"
fi

# Create VMX file with Easy Install settings
echo "Creating VM configuration file..."
cat > "$VM_PATH" << 'EOFMARKER'
.encoding = "UTF-8"
config.version = "8"
virtualHW.version = "18"
numvcpus = "2"
memsize = "4096"
displayName = "Windows 10 TikTok Bot (Easy Install)"
guestOS = "windows9-64"
ethernet0.present = "TRUE"
ethernet0.connectionType = "nat"
ethernet0.virtualDev = "e1000"
ethernet0.wakeOnPcktRcv = "FALSE"
ethernet0.addressType = "generated"
usb.present = "TRUE"
sound.present = "TRUE"
sound.virtualDev = "hdaudio"
sound.autodetect = "TRUE"
mks.enable3d = "TRUE"
isolation.tools.hgfs.disable = "FALSE"
ide0:0.present = "TRUE"
ide0:0.fileName = "ISOPATH"
ide0:0.deviceType = "cdrom-image"
ide0:0.startConnected = "TRUE"
ide1:0.present = "TRUE"
ide1:0.fileName = "VMDKPATH"
ide1:0.deviceType = "disk"
bios.bootDelay = "5000"
bios.bootOrder = "cdrom,hdd"
annotation = "Windows 10 VM for TikTok Bot Farm"
firmware = "efi"
tools.syncTime = "TRUE"
priority.grabbed = "normal"
priority.ungrabbed = "normal"
EOFMARKER

# Replace placeholders with actual paths
sed -i '' "s|ISOPATH|$ISO_PATH|g" "$VM_PATH"
sed -i '' "s|VMDKPATH|$VM_DIR/$VM_NAME.vmdk|g" "$VM_PATH"

# Start VM
echo "Starting VM with Easy Install..."
vmrun -T fusion start "$VM_PATH" gui

echo "VM created and started with Easy Install."
echo "Windows 10 installation should proceed automatically."
echo ""
echo "Default Windows credentials:"
echo "Username: Administrator"
echo "Password: VMware1!"
echo ""
echo "After Windows is installed, you'll need to:"
echo "1. Install LDPlayer from https://www.ldplayer.net/"
echo "2. Install TikMatrix from https://tikmatrix.com/"
echo "3. Configure them according to the TikTok Bot Farm documentation"
