#!/bin/bash

# Script to check the status of the Easy Install VM

# Define VM path
VM_PATH="vm-data/Windows10TikTokBot_EasyInstall/Windows10TikTokBot_EasyInstall.vmx"

# Check if VM exists
if [ ! -f "$VM_PATH" ]; then
    echo "VM configuration file not found: $VM_PATH"
    exit 1
fi

# Check if the VM is running
if ! vmrun -T fusion list | grep -q "$VM_PATH"; then
    echo "VM is not running. Starting VM..."
    vmrun -T fusion start "$VM_PATH" gui
    echo "Waiting for VM to boot..."
    sleep 30
fi

# Try to get the IP address (this requires VMware Tools to be running)
echo "Checking if VMware Tools are running..."
IP_OUTPUT=$(vmrun -T fusion getGuestIPAddress "$VM_PATH" 2>&1)
IP_STATUS=$?

if [ $IP_STATUS -eq 0 ] && [[ ! "$IP_OUTPUT" == *"Error"* ]]; then
    IP="$IP_OUTPUT"
    echo "VMware Tools are running successfully!"
    echo "VM IP address: $IP"
    echo ""
    echo "Windows 10 installation is complete."
    echo "You can now proceed with installing LDPlayer and TikMatrix."
    echo ""
    echo "Next steps:"
    echo "1. Install LDPlayer from https://www.ldplayer.net/"
    echo "2. Install TikMatrix from https://tikmatrix.com/"
    echo "3. Configure them according to the TikTok Bot Farm documentation"
else
    echo "VMware Tools are not running or Windows installation is still in progress."
    echo ""
    echo "If Windows installation is complete but VMware Tools are not running:"
    echo "1. In VMware Fusion menu, select Virtual Machine > Install VMware Tools"
    echo "2. In Windows, open File Explorer and navigate to the DVD drive"
    echo "3. Run setup64.exe"
    echo "4. Follow the installation prompts"
    echo "5. Restart the VM when prompted"
    echo ""
    echo "If Windows installation is still in progress:"
    echo "1. Wait for the installation to complete"
    echo "2. Windows will automatically log in with the default credentials:"
    echo "   Username: Administrator"
    echo "   Password: VMware1!"
fi
