#!/bin/bash

# Script to manually install VMware Tools in a Windows VM

# Define VM path
VM_PATH="/Users/<USER>/Documents/GitHub/TikTokBotFarmMVP/scripts/vmware-setup/vm-data/Windows10TikTokBot_EasyInstall/Windows10TikTokBot_EasyInstall.vmx"

# Check if VM is running
if ! vmrun -T fusion list | grep -q "$VM_PATH"; then
    echo "VM is not running. Starting VM..."
    vmrun -T fusion start "$VM_PATH" gui
    echo "Waiting for VM to boot..."
    sleep 30
fi

# Stop the VM to reset the VMware Tools installation state
echo "Stopping the VM to reset VMware Tools installation state..."
vmrun -T fusion stop "$VM_PATH" soft
echo "Waiting for VM to shut down..."
sleep 10

# Start the VM again
echo "Starting the VM again..."
vmrun -T fusion start "$VM_PATH" gui
echo "Waiting for VM to boot..."
sleep 30

# Mount the VMware Tools ISO
echo "Mounting VMware Tools ISO..."
vmrun -T fusion installTools "$VM_PATH"
echo "VMware Tools installation media has been mounted in the VM."

echo ""
echo "Please complete the installation manually in the VM:"
echo "1. Log in to the VM with username 'user' and password 'PHF700nw'"
echo "2. Open File Explorer in Windows"
echo "3. Navigate to the VMware Tools DVD (usually drive D:)"
echo "4. Run setup64.exe"
echo "5. Follow the installation prompts"
echo "6. Restart the VM when prompted"
echo ""
echo "After VMware Tools is installed, you can run the LDPlayer and TikMatrix installation script:"
echo "./run_vm_installer.sh"
