# Project Rules for TikTok Bot Farm System MVP

## Description
The TikTok Bot Farm System MVP automates the management of 10 TikTok accounts on a single Windows computer using [LDPlayer](https://www.ldplayer.net/) emulators and [TikMatrix](https://tikmatrix.com/) software. These rules guide developers and AI Coding Agents in setting up, configuring, and operating the system to ensure security, compliance with [TikTok’s Community Guidelines](https://www.tiktok.com/community-guidelines), and scalability for future expansion.

## Standards

### 1. System Setup
- **Host Machine**: Use Windows 10 or later with at least 8GB RAM (16GB recommended) and a multi-core CPU supporting virtualization (Intel VT-x or AMD-V).
- **LDPlayer**:
  - Install the latest version from the [LDPlayer website](https://www.ldplayer.net/).
  - Create 10 instances, each with the TikTok app installed via Google Play Store.
  - Enable ADB debugging on each instance (Settings > Others > ADB Debugging > Open local connection).
  - Verify connectivity by running `adb devices` in a command prompt to list all instances (e.g., `emulator-5554`).
- **TikMatrix**:
  - Install the latest version from the [TikMatrix website](https://tikmatrix.com/).
  - Ensure TikMatrix detects all 10 LDPlayer instances upon launch, displaying them in the dashboard.

### 2. Automation Configuration
- **Register Script**:
  - Configure to create new TikTok accounts using unique email addresses or phone numbers.
  - Optionally integrate with SMS verification services like [5sim](https://5sim.net/) or [SMSPool](https://smspool.net/) for account verification.
  - Limit account creation to 1-2 per instance per session to avoid detection.
- **Train Script**:
  - Set up to simulate user activity (e.g., liking, following, commenting) with random delays (5-15 seconds) between actions.
  - Target relevant accounts or hashtags aligned with account niches to enhance authenticity.
  - Limit actions to 10-20 per session to mimic natural behavior.
- **Publish Script**:
  - Configure to post high-quality content (videos, images) with appropriate captions and hashtags.
  - Schedule posts at optimal times (e.g., 1-2 posts daily during peak engagement hours).
  - Ensure content complies with TikTok’s guidelines to avoid violations.

### 3. Security
- **Proxies**:
  - Acquire residential proxies from reputable providers like [5sim](https://5sim.net/) or [SMSPool](https://smspool.net/).
  - Assign a unique proxy to each LDPlayer instance via Settings > Network > Proxy.
  - Test proxy connectivity to ensure each instance uses a distinct IP address.
- **Account Monitoring**:
  - Use TikMatrix’s Account Manager to check account statuses (e.g., active, warming, suspended) at least daily.
  - Promptly remove or adjust configurations for suspended accounts to prevent further issues.
- **Data Protection**:
  - Encrypt TikTok account credentials stored locally using AES-256 encryption.
  - Secure the host machine with user authentication to prevent unauthorized access.

### 4. Compliance
- **Natural Behavior**: Configure all scripts to include random delays and varied action sequences to simulate authentic user activity.
- **Content Quality**: Post only high-quality, platform-appropriate content that adheres to [TikTok’s Community Guidelines](https://www.tiktok.com/community-guidelines).
- **Policy Updates**: Monitor TikTok’s terms of service weekly and adjust script configurations or content strategies as needed.

### 5. Operation
- **Script Execution**:
  - Run scripts incrementally, starting with 1-2 accounts to test configurations before scaling to all 10.
  - Monitor real-time execution status in TikMatrix’s dashboard to catch errors early.
- **Logging**:
  - Use TikMatrix’s Log Viewer to review script execution logs after each run.
  - Analyze logs for errors (e.g., invalid credentials, connectivity issues) and resolve promptly.
- **Task Scheduling**:
  - Use TikMatrix’s scheduling feature to automate recurring tasks (e.g., daily posting at 8 AM).
  - Distribute tasks across different times to avoid simultaneous activity that may trigger detection.

## Quality Controls
- **Validation**: Verify each setup step (e.g., LDPlayer installation, TikMatrix detection) before proceeding to the next task.
- **Error Handling**: Implement retry mechanisms for failed script executions (up to three retries) and log detailed error messages.
- **Performance**: Ensure the host machine maintains stable performance with 10 LDPlayer instances, targeting average CPU usage below 10% per instance.

## Best Practices
- **Documentation**: Maintain detailed records of system configurations, script parameters, proxy details, and account statuses for troubleshooting and future reference.
- **Backup**: Regularly back up configuration files and encrypted account data to prevent loss.
- **Updates**: Check for LDPlayer and TikMatrix updates monthly to ensure compatibility and access to new features.

## AI Assistance Guidelines
For AI Coding Agents assisting with this project:
- **Setup Assistance**: Provide clear, step-by-step instructions for installing and configuring LDPlayer and TikMatrix, including links to official documentation (e.g., [LDPlayer](https://www.ldplayer.net/), [TikMatrix](https://tikmatrix.com/)).
- **Script Configuration**: Suggest best practices for script setup, such as using random delays (5-15 seconds) and targeting niche-relevant accounts or hashtags.
- **Security**: Emphasize the importance of unique proxies for each instance and recommend testing proxy connectivity before script execution.
- **Compliance**: Include reminders to check [TikTok’s Community Guidelines](https://www.tiktok.com/community-guidelines) and configure scripts to avoid spammy behavior or low-quality content.
- **Operation**: Assist with interpreting TikMatrix logs and managing accounts, providing actionable steps to resolve common issues (e.g., connectivity errors, account suspensions).
- **Task Management**: Help prioritize tasks based on the provided task list, ensuring dependencies are respected, and suggest project management tools (e.g., Trello, Asana) for tracking progress.

## Notes
- These rules ensure the system is set up securely, operates efficiently, and complies with TikTok’s policies.
- Developers must adhere to these guidelines when configuring or modifying the system.
- AI Coding Agents should use these rules as context to provide accurate, project-specific assistance, aligning with the project’s goals of automation, security, and compliance.